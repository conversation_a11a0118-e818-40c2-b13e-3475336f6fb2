package com.czb.hn.dto.response.briefing;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MediaDistributionDto {
    @Schema(description = "媒体名称")
    private String mediaName;

    @Schema(description = "数量")
    private Long count;

    @Schema(description = "占比")
    private Double proportion;
}
