package com.czb.hn.web.controllers;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.response.briefing.HighFrequencyWordDto;
import com.czb.hn.dto.response.briefing.HistogramDto;
import com.czb.hn.dto.response.briefing.HistogramGroupDto;
import com.czb.hn.dto.response.briefing.HistogramSummeryDto;
import com.czb.hn.dto.response.briefing.MediaTierDto;
import com.czb.hn.dto.response.briefing.MediaDistributionDto;
import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContextHolder;
import com.czb.hn.dto.workbench.AlertListItemDTO;
import com.czb.hn.dto.workbench.InformationStatsDTO;
import com.czb.hn.dto.workbench.WorkbenchStatsDTO;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.entity.EnterpriseSubscription;
import com.czb.hn.service.business.BillingService;
import com.czb.hn.service.business.ElasticsearchBriefingService;
import com.czb.hn.service.business.AlertSearchService;
import com.czb.hn.service.business.ElasticsearchSearchService;
import com.czb.hn.service.business.InformationStatsService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.user.UserLoginRecordService;
import com.czb.hn.util.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/workbench")
@Tag(name = "WorkBenchController", description = "工作台")
public class WorkBenchController {

    private static final Logger logger = LoggerFactory.getLogger(WorkBenchController.class);

    @Autowired
    private ElasticsearchSearchService elasticsearchSearchService;

    @Autowired
    private ElasticsearchBriefingService elasticsearchBriefingService;

    @Autowired
    private UserLoginRecordService userLoginRecordService;

    @Autowired
    private BillingService billingService;

    @Autowired
    private PlanService planService;

    @Autowired
    private InformationStatsService informationStatsService;

    @Autowired
    private AlertSearchService alertSearchService;

    @GetMapping("/hot-news")
    @Operation(summary = "获取工作台-热门信息", description = "获取工作台-热门信息")
    public ResponseEntity<ApiResponse<List<SinaNewsSearchResponseDto>>> hotNews(
            @Parameter(description = "方案ID") @RequestParam(required = false) Long planId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的热门新闻: startTime={}, endTime={}", planId, startTime, endTime);
            LoginUser user = LoginUserContextHolder.getUser();
            if (user == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户信息不存在"));
            }

            List<Long> planIds = getPlans(planId, user);

            SearchRequestDto requestDto = new SearchRequestDto();
            requestDto.setStartTime(startTime);
            requestDto.setEndTime(endTime);
            requestDto.setSortRule(3);
            requestDto.setMatchMethod(0);
            requestDto.setSimilarityDisplayRule(false);

            List<SinaNewsSearchResponseDto> results = new ArrayList<>();

            for (Long it : planIds) {
                requestDto.setPlanId(it);
                List<SinaNewsSearchResponseDto> sinaNewsSearchResponseDtos = elasticsearchSearchService.SinaNewsMonitor(requestDto, 20, 1);
                results.addAll(sinaNewsSearchResponseDtos);
             }

            List<SinaNewsSearchResponseDto> responseDtos = results.stream()
                    .sorted((o1, o2) -> o2.getPublishTime().compareTo(o1.getPublishTime()))
                    .limit(20)
                    .toList();

            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDtos));
        } catch (IllegalArgumentException e) {
            logger.warn("热门信息参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("热门信息获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }



    @GetMapping("/sensitive-info-trend")
    @Operation(summary = "获取工作台-敏感信息趋势", description = "获取工作台-敏感信息趋势")
    public ResponseEntity<ApiResponse<List<HistogramGroupDto>>> sensitiveInfoTrend(
            @Parameter(description = "方案ID") @RequestParam(required = false) Long planId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的敏感信息趋势: startTime={}, endTime={}", planId, startTime, endTime);
            LoginUser user = LoginUserContextHolder.getUser();
            if (user == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户信息不存在"));
            }

            List<Long> planIds = getPlans(planId, user);
            List<Integer> sensitivityType = List.of(InformationSensitivityType.SENSITIVE.getValue(), InformationSensitivityType.NON_SENSITIVE.getValue(), InformationSensitivityType.NEUTRAL.getValue());


            // 初始化聚合结果容器
            Map<String, HistogramGroupDto> timeGroupMap = new HashMap<>();

            // 遍历所有计划ID，累加数据
            for (Long currentPlanId : planIds) {
                // 获取单个计划的趋势数据
                HistogramSummeryDto histogramSummery = elasticsearchBriefingService.getSensitiveInfoTrend(
                        startTime, endTime, currentPlanId, sensitivityType, null, null, null, null, null);

                // 累加敏感数据
            }
            



            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", ));
        } catch (IllegalArgumentException e) {
            logger.warn("趋势图参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("趋势图获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }

    @GetMapping("/media-source-distribution")
    @Operation(summary = "获取工作台-媒体来源占比", description = "获取工作台-媒体来源占比")
    public ResponseEntity<ApiResponse<List<MediaDistributionDto>>> mediaDistribution(
            @Parameter(description = "方案ID") @RequestParam() Long planId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的媒体来源占比: startTime={}, endTime={}", planId, startTime, endTime);
            List<Integer> sensitivityType = new ArrayList<>();
            sensitivityType.add(InformationSensitivityType.SENSITIVE.getValue());
            sensitivityType.add(InformationSensitivityType.NON_SENSITIVE.getValue());
            sensitivityType.add(InformationSensitivityType.NEUTRAL.getValue());
            List<MediaDistributionDto> responseDtos = elasticsearchBriefingService.MediaDistribution(startTime, endTime,
                    planId, sensitivityType, null, null, null, null, null);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDtos));
        } catch (IllegalArgumentException e) {
            logger.warn("媒体来源占比参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("媒体来源占比获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }

    @GetMapping("high-frequency-word")
    @Operation(summary = "获取工作台-高频词", description = "获取工作台-高频词")
    public ResponseEntity<ApiResponse<List<HighFrequencyWordDto>>> highFrequencyWord(
            @Parameter(description = "方案ID") @RequestParam() Long planId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的高频词: startTime={}, endTime={}", planId, startTime, endTime);
            List<Integer> sensitivityType = new ArrayList<>();
            sensitivityType.add(1);
            sensitivityType.add(2);
            sensitivityType.add(3);
            List<HighFrequencyWordDto> responseDtos = elasticsearchBriefingService.HighFrequencyWords(startTime,
                    endTime, planId, null, null, null, null, null, null);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDtos));
        } catch (IllegalArgumentException e) {
            logger.warn("高频词云参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("高频词云获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }

    @GetMapping("media-tier-distribution")
    @Operation(summary = "获取工作台-媒体级别分布", description = "获取工作台-媒体级别分布")
    public ResponseEntity<ApiResponse<List<MediaTierDto>>> mediaTierDistribution(
            @Parameter(description = "方案ID") @RequestParam() Long planId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的媒体级别分布: startTime={}, endTime={}", planId, startTime, endTime);
            List<Integer> sensitivityType = new ArrayList<>();
            sensitivityType.add(InformationSensitivityType.SENSITIVE.getValue());
            sensitivityType.add(InformationSensitivityType.NON_SENSITIVE.getValue());
            sensitivityType.add(InformationSensitivityType.NEUTRAL.getValue());
            List<MediaTierDto> responseDto = elasticsearchBriefingService.MediaDetail(startTime, endTime, planId,
                    sensitivityType, null, null, null, null, null);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDto));
        } catch (IllegalArgumentException e) {
            logger.warn("媒体级别分布参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("媒体级别分布获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取工作台统计信息", description = "获取用户上次登录时间、近7天登录次数、企业账号到期时间和方案数")
    public ResponseEntity<ApiResponse<WorkbenchStatsDTO>> getWorkbenchStats() {
        try {
            // 获取当前用户信息
            String currentUserId = UserContext.getCurrentUserId();
            String currentEnterpriseCode = UserContext.getCurrentEnterpriseCode();
            String currentEnterpriseId = UserContext.getCurrentEnterpriseId();
            if (currentUserId == null) {
                logger.warn("User not logged in when accessing workbench stats");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            if (currentEnterpriseCode == null || currentEnterpriseId == null) {
                logger.warn("Enterprise code or ID not found for user: {}", currentUserId);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("企业信息未找到"));
            }

            logger.info("Getting workbench stats for user: {} in enterprise: {}", currentUserId, currentEnterpriseCode);

            // 获取用户上次登录时间
            LocalDateTime lastLoginTime = userLoginRecordService.getLastLoginTime(currentUserId);

            // 获取近7天登录次数
            long sevenDayLoginCount = userLoginRecordService.getLoginCountInDays(currentUserId, 7);

            // 获取企业账号到期时间
            LocalDateTime enterpriseExpirationDate = null;
            Optional<EnterpriseSubscription> subscription = billingService
                    .getCurrentSubscription(currentEnterpriseCode);
            if (subscription.isPresent()) {
                enterpriseExpirationDate = subscription.get().getEndDate().atTime(23, 59, 59);

            }
            // 获取企业方案数量
            List<com.czb.hn.dto.PlanDTO> plans = planService.getPlansByEnterpriseId(currentEnterpriseId);
            long planCount = plans.size();

            // 构建响应DTO
            WorkbenchStatsDTO statsDTO = new WorkbenchStatsDTO(
                    lastLoginTime,
                    sevenDayLoginCount,
                    enterpriseExpirationDate,
                    planCount);

            logger.info(
                    "Workbench stats retrieved successfully for user: {}, lastLogin: {}, 7dayCount: {}, expiration: {}, planCount: {}",
                    currentUserId, lastLoginTime, sevenDayLoginCount, enterpriseExpirationDate, planCount);

            return ResponseEntity.ok(ApiResponse.success(statsDTO));

        } catch (Exception e) {
            logger.error("Failed to get workbench stats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取工作台统计信息失败: " + e.getMessage()));
        }
    }

    @GetMapping("/information-stats")
    @Operation(summary = "获取信息统计总量", description = "统计舆情信息总量、舆情敏感信息总量、预警信息总量，支持指定方案或企业下所有方案")
    public ResponseEntity<ApiResponse<InformationStatsDTO>> getInformationStats(
            @Parameter(description = "方案ID（可选，不传则统计企业下所有方案）") @RequestParam(required = false) Long planId,
            @Parameter(description = "开始时间", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            // 获取当前用户信息
            String currentUserId = UserContext.getCurrentUserId();
            String currentEnterpriseId = UserContext.getCurrentEnterpriseId();

            if (currentUserId == null) {
                logger.warn("User not logged in when accessing information stats");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            if (currentEnterpriseId == null) {
                logger.warn("Enterprise ID not found for user: {}", currentUserId);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("企业信息未找到"));
            }

            // 验证时间参数
            if (startTime.isAfter(endTime)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("开始时间不能晚于结束时间"));
            }

            logger.info("Getting information stats for user: {}, enterprise: {}, plan: {}, time range: {} to {}",
                    currentUserId, currentEnterpriseId, planId, startTime, endTime);

            InformationStatsDTO statsDTO;

            if (planId != null) {
                // 统计指定方案
                statsDTO = informationStatsService.getInformationStats(planId, startTime, endTime);
            } else {
                // 统计企业下所有方案
                statsDTO = informationStatsService.getEnterpriseInformationStats(currentEnterpriseId, startTime,
                        endTime);
            }

            logger.info("Information stats retrieved successfully: total={}, sensitive={}, alerts={}",
                    statsDTO.totalInformationCount(), statsDTO.sensitiveInformationCount(), statsDTO.alertCount());

            return ResponseEntity.ok(ApiResponse.success(statsDTO));

        } catch (IllegalArgumentException e) {
            logger.warn("Information stats parameter error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("参数错误: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Failed to get information stats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取信息统计失败: " + e.getMessage()));
        }
    }

    @GetMapping("/alert-list")
    @Operation(summary = "获取预警信息列表", description = "获取工作台预警信息列表，支持指定方案或企业下所有方案，支持自定义时间段查询，按预警时间降序排列")
    public ResponseEntity<ApiResponse<List<AlertListItemDTO>>> getAlertList(
            @Parameter(description = "方案ID（可选，不传则获取企业下所有方案的预警信息）") @RequestParam(required = false) Long planId,
            @Parameter(description = "开始时间（可选，格式：yyyy-MM-dd HH:mm:ss）") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间（可选，格式：yyyy-MM-dd HH:mm:ss）") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "返回记录数限制，默认20，最大100") @RequestParam(required = false, defaultValue = "20") Integer limit) {
        try {
            // 获取当前用户信息
            String currentUserId = UserContext.getCurrentUserId();
            String currentEnterpriseId = UserContext.getCurrentEnterpriseId();

            if (currentUserId == null) {
                logger.warn("User not logged in when accessing alert list");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            if (currentEnterpriseId == null) {
                logger.warn("Enterprise ID not found for user: {}", currentUserId);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("企业信息未找到"));
            }

            // 验证limit参数
            if (limit != null && limit <= 0) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("limit参数必须大于0"));
            }

            // 验证时间范围
            if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("开始时间不能晚于结束时间"));
            }

            logger.info(
                    "Getting alert list for user: {}, enterprise: {}, plan: {}, startTime: {}, endTime: {}, limit: {}",
                    currentUserId, currentEnterpriseId, planId, startTime, endTime, limit);

            List<AlertListItemDTO> alertList;

            // 判断是否使用时间段查询
            boolean useTimeRange = startTime != null || endTime != null;

            if (planId != null) {
                // 获取指定方案的预警信息列表
                if (useTimeRange) {
                    alertList = alertSearchService.getAlertListByPlanIdAndTimeRange(planId, startTime, endTime, limit);
                } else {
                    alertList = alertSearchService.getAlertListByPlanId(planId, limit);
                }
            } else {
                // 获取企业下所有方案的预警信息列表
                if (useTimeRange) {
                    alertList = alertSearchService.getAlertListByEnterpriseIdAndTimeRange(currentEnterpriseId,
                            startTime, endTime, limit);
                } else {
                    alertList = alertSearchService.getAlertListByEnterpriseId(currentEnterpriseId, limit);
                }
            }

            logger.info("Alert list retrieved successfully: {} alerts returned", alertList.size());

            return ResponseEntity.ok(ApiResponse.success(alertList));

        } catch (IllegalArgumentException e) {
            logger.warn("Alert list parameter error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("参数错误: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Failed to get alert list", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预警信息列表失败: " + e.getMessage()));
        }
    }

    @GetMapping("/sensitive-list")
    @Operation(summary = "获取敏感信息列表", description = "获取敏感信息列表")
    public ResponseEntity<ApiResponse<List<SinaNewsSearchResponseDto>>> getSensitiveList(
            @Parameter(description = "方案ID") @RequestParam() Long planId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        try {
            logger.info("获取方案{}的敏感信息: startTime={}, endTime={}", planId, startTime, endTime);

            SearchRequestDto requestDto = new SearchRequestDto();
            requestDto.setPlanId(planId);
            requestDto.setStartTime(startTime);
            requestDto.setEndTime(endTime);
            requestDto.setSortRule(1);
            requestDto.setMatchMethod(0);
            requestDto.setSimilarityDisplayRule(false);
            requestDto.setSensitivityType(List.of(1));
            List<SinaNewsSearchResponseDto> responseDtos = elasticsearchSearchService.SinaNewsMonitor(requestDto, 20, 1);

            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取成功", responseDtos));
        } catch (IllegalArgumentException e) {
            logger.warn("敏感信息参数错误: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponse<>("ERROR", "参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            logger.error("敏感信息获取失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "搜索失败: " + e.getMessage(), null));
        }
    }


    private @NotNull List<Long> getPlans(Long planId, LoginUser user) {
        List<Long> planIds = new ArrayList<>();

        if (planId == null) {
            List<PlanDTO> allPlans = new ArrayList<>();
            if (user.isSystemAdmin()) {
                allPlans = planService.getAllPlans();
            }

            if (user.isEnterpriseAdmin()) {
                allPlans = planService.getPlansByEnterpriseId(user.getPrimaryGroupId());
            }
            planIds = allPlans.stream().map(PlanDTO::id).toList();
        } else {
            planIds.add(planId);
        }
        return planIds;
    }

}
